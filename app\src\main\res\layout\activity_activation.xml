<?xml version="1.0" encoding="utf-8"?>
<layout>
    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.activity.ActivationActivity">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/common_margin"
            android:gravity="center"
            android:orientation="vertical">

            <!-- Online Activation Section -->
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:textColor="@color/colorAccent"
                android:layout_gravity="start"
                android:text="@string/active_online"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:layout_gravity="start"
                android:text="@string/notice_active_online"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <!-- APP_ID Input -->
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:layout_gravity="start"
                android:text="@string/app_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <EditText
                android:id="@+id/etAppId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@{appId}"
                android:hint="@string/app_id"/>

            <!-- SDK_KEY Input -->
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:layout_gravity="start"
                android:text="@string/sdk_key"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <EditText
                android:id="@+id/etSdkKey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@{sdkKey}"
                android:hint="@string/sdk_key"/>

            <!-- ACTIVE_KEY Input -->
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:layout_gravity="start"
                android:text="@string/active_key"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <EditText
                android:id="@+id/etActiveKey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@{activeKey}"
                android:hint="@string/active_key"/>

            <Button
                android:text="@string/active_online"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="activeOnline"/>

            <Button
                android:text="@string/read_local_config_and_active"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="activeFromConfigFile"/>

            <!-- Offline Activation Section -->
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:textColor="@color/colorAccent"
                android:layout_gravity="start"
                android:text="@string/active_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:layout_gravity="start"
                android:text="@string/notice_active_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <Button
                android:text="@string/active_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="activeOffline"/>

            <!-- Device Fingerprint Section -->
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:textColor="@color/colorAccent"
                android:layout_gravity="start"
                android:text="@string/copy_device_finger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <Button
                android:text="@string/copy_device_finger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="copyDeviceFinger"/>
        </LinearLayout>
    </ScrollView>
    <data>
        <variable
            name="appId"
            type="String" />
        <variable
            name="sdkKey"
            type="String" />
        <variable
            name="activeKey"
            type="String" />
    </data>
</layout>